import { useState, useEffect } from 'react';

import axios from 'axios';

function App() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);

  // 简单的 API 调用
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await axios.get('https://jsonplaceholder.typicode.com/users');
      setUsers(response.data.slice(0, 3)); // 只取前3个用户
    } catch (error) {
      console.error('API 调用失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <>
      <div className="api-demo">
        <h2>📡 API 调用演示</h2>
        <button onClick={fetchUsers} disabled={loading}>
          {loading ? '加载中...' : '刷新用户'}
        </button>

        {users.length > 0 && (
          <div style={{ marginTop: '20px' }}>
            <h3>用户列表:</h3>
            {users.map((user:any) => (
              <div
                key={user.id}
                style={{ border: '1px solid #ccc', padding: '10px', margin: '5px' }}
              >
                <h4>{user.name}</h4>
                <p>邮箱: {user.email}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      <p className="read-the-docs">这是一个简单的 API 调用演示</p>
    </>
  );
}

export default App;

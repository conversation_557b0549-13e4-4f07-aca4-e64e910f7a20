#!/bin/bash

# Tauri Android 开发环境检查脚本

echo "🔍 检查 Tauri Android 开发环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 未找到${NC}"
        return 1
    fi
}

check_path() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $2 路径存在: $1${NC}"
        return 0
    else
        echo -e "${RED}❌ $2 路径不存在: $1${NC}"
        return 1
    fi
}

# 检查基本工具
echo -e "\n📋 基本工具检查:"
check_command "java"
check_command "node"
check_command "npm"
check_command "cargo"
check_command "rustc"

# 检查环境变量
echo -e "\n🌍 环境变量检查:"
if [ -n "$ANDROID_HOME" ]; then
    echo -e "${GREEN}✅ ANDROID_HOME: $ANDROID_HOME${NC}"
    check_path "$ANDROID_HOME" "Android SDK"
else
    echo -e "${RED}❌ ANDROID_HOME 未设置${NC}"
fi

if [ -n "$NDK_HOME" ]; then
    echo -e "${GREEN}✅ NDK_HOME: $NDK_HOME${NC}"
    check_path "$NDK_HOME" "Android NDK"
else
    echo -e "${RED}❌ NDK_HOME 未设置${NC}"
fi

if [ -n "$JAVA_HOME" ]; then
    echo -e "${GREEN}✅ JAVA_HOME: $JAVA_HOME${NC}"
    check_path "$JAVA_HOME" "Java JDK"
else
    echo -e "${RED}❌ JAVA_HOME 未设置${NC}"
fi

# 检查 Android 工具
echo -e "\n🤖 Android 工具检查:"
check_command "adb"
check_command "emulator"

# 检查 Tauri
echo -e "\n🦀 Tauri 检查:"
if [ -f "src-tauri/Cargo.toml" ]; then
    echo -e "${GREEN}✅ Tauri 项目结构正确${NC}"
else
    echo -e "${RED}❌ 未找到 Tauri 项目${NC}"
fi

# 检查自定义脚本
if [ -f "src-tauri/tauri" ] && [ -x "src-tauri/tauri" ]; then
    echo -e "${GREEN}✅ 自定义 tauri 脚本存在且可执行${NC}"
else
    echo -e "${YELLOW}⚠️  自定义 tauri 脚本不存在或不可执行${NC}"
fi

echo -e "\n🎯 环境检查完成！"
echo -e "\n📝 如果有红色错误，请按照文档安装相应组件。"

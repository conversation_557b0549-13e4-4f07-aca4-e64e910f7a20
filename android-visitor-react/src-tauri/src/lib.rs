use serde::{Deserialize, Serialize};
use tauri::command;

// 设备信息结构体
#[derive(Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_name: String,
    pub os_version: String,
    pub app_version: String,
    pub device_id: String,
    pub battery_level: u8,
    pub is_charging: bool,
}

// GPS 位置信息结构体
#[derive(Serialize, Deserialize)]
pub struct LocationInfo {
    pub latitude: f64,
    pub longitude: f64,
    pub accuracy: f32,
    pub timestamp: u64,
}

// 系统信息结构体
#[derive(Serialize, Deserialize)]
pub struct SystemInfo {
    pub total_memory: u64,
    pub available_memory: u64,
    pub cpu_usage: f32,
    pub storage_total: u64,
    pub storage_available: u64,
}

// 网络信息结构体
#[derive(Serialize, Deserialize)]
pub struct NetworkInfo {
    pub connection_type: String,
    pub is_connected: bool,
    pub signal_strength: i32,
    pub ip_address: String,
}

// Tauri 命令：获取设备信息
#[command]
async fn get_device_info() -> Result<DeviceInfo, String> {
    // 模拟设备信息
    let device_info = DeviceInfo {
        device_name: "Android Simulator".to_string(),
        os_version: "Android 14 (API 34)".to_string(),
        app_version: "1.0.0".to_string(),
        device_id: "emulator-5554".to_string(),
        battery_level: 85,
        is_charging: false,
    };

    Ok(device_info)
}

// Tauri 命令：获取 GPS 位置
#[command]
async fn get_location() -> Result<LocationInfo, String> {
    // 模拟 GPS 位置（北京天安门）
    let location = LocationInfo {
        latitude: 39.9042,
        longitude: 116.4074,
        accuracy: 10.0,
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
    };

    Ok(location)
}

// Tauri 命令：获取系统信息
#[command]
async fn get_system_info() -> Result<SystemInfo, String> {
    // 模拟系统信息
    let system_info = SystemInfo {
        total_memory: 8 * 1024 * 1024 * 1024, // 8GB
        available_memory: 4 * 1024 * 1024 * 1024, // 4GB
        cpu_usage: 25.5,
        storage_total: 64 * 1024 * 1024 * 1024, // 64GB
        storage_available: 32 * 1024 * 1024 * 1024, // 32GB
    };

    Ok(system_info)
}

// Tauri 命令：获取网络信息
#[command]
async fn get_network_info() -> Result<NetworkInfo, String> {
    // 模拟网络信息
    let network_info = NetworkInfo {
        connection_type: "WiFi".to_string(),
        is_connected: true,
        signal_strength: -45, // dBm
        ip_address: "*************".to_string(),
    };

    Ok(network_info)
}

// Tauri 命令：模拟震动
#[command]
async fn vibrate(duration: u64) -> Result<String, String> {
    // 模拟震动功能
    println!("📳 模拟震动 {} 毫秒", duration);
    Ok(format!("震动 {} 毫秒完成", duration))
}

// Tauri 命令：显示系统通知
#[command]
async fn show_notification(title: String, message: String) -> Result<String, String> {
    // 模拟系统通知
    println!("🔔 通知: {} - {}", title, message);
    Ok("通知已发送".to_string())
}

// Tauri 命令：读取文件
#[command]
async fn read_file(file_path: String) -> Result<String, String> {
    // 模拟文件读取
    match std::fs::read_to_string(&file_path) {
        Ok(content) => Ok(content),
        Err(e) => Err(format!("读取文件失败: {}", e)),
    }
}

// Tauri 命令：写入文件
#[command]
async fn write_file(file_path: String, content: String) -> Result<String, String> {
    // 模拟文件写入
    match std::fs::write(&file_path, content) {
        Ok(_) => Ok("文件写入成功".to_string()),
        Err(e) => Err(format!("写入文件失败: {}", e)),
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .invoke_handler(tauri::generate_handler![
        get_device_info,
        get_location,
        get_system_info,
        get_network_info,
        vibrate,
        show_notification,
        read_file,
        write_file
    ])
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }
      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}

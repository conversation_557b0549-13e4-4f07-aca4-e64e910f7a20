import { useState, useEffect } from 'react';
import axios from 'axios';
import { useHardware } from './hooks/useHardware';
import type { DeviceInfo, LocationInfo, SystemInfo, NetworkInfo } from './hooks/useHardware';

function App() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);

  // 硬件信息状态
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [locationInfo, setLocationInfo] = useState<LocationInfo | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [networkInfo, setNetworkInfo] = useState<NetworkInfo | null>(null);

  // 使用硬件 Hook
  const hardware = useHardware();

  // 简单的 API 调用
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await axios.get('https://jsonplaceholder.typicode.com/users');
      setUsers(response.data.slice(0, 3)); // 只取前3个用户
    } catch (error) {
      console.error('API 调用失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 硬件功能处理函数
  const handleGetDeviceInfo = async () => {
    const result = await hardware.getDeviceInfo();
    if (result) setDeviceInfo(result);
  };

  const handleGetLocation = async () => {
    const result = await hardware.getLocation();
    if (result) setLocationInfo(result);
  };

  const handleGetSystemInfo = async () => {
    const result = await hardware.getSystemInfo();
    if (result) setSystemInfo(result);
  };

  const handleGetNetworkInfo = async () => {
    const result = await hardware.getNetworkInfo();
    if (result) setNetworkInfo(result);
  };

  const handleVibrate = async () => {
    await hardware.vibrate(1000); // 震动1秒
  };

  const handleShowNotification = async () => {
    await hardware.showNotification('测试通知', '这是来自 Tauri 应用的通知！');
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <>
      <div className="api-demo">
        <h2>📡 API 调用演示</h2>
        <button onClick={fetchUsers} disabled={loading}>
          {loading ? '加载中...' : '刷新用户'}
        </button>

        {users.length > 0 && (
          <div style={{ marginTop: '20px' }}>
            <h3>用户列表:</h3>
            {users.map((user) => (
              <div
                key={user.id}
                style={{ border: '1px solid #ccc', padding: '10px', margin: '5px' }}
              >
                <h4>{user.name}</h4>
                <p>邮箱: {user.email}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 硬件功能演示 */}
      <div className="hardware-demo" style={{ marginTop: '30px' }}>
        <h2>📱 硬件功能演示</h2>

        {hardware.error && (
          <div style={{ color: 'red', marginBottom: '10px' }}>错误: {hardware.error}</div>
        )}

        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '10px',
            marginBottom: '20px',
          }}
        >
          <button onClick={handleGetDeviceInfo} disabled={hardware.loading}>
            📱 获取设备信息
          </button>
          <button onClick={handleGetLocation} disabled={hardware.loading}>
            📍 获取位置信息
          </button>
          <button onClick={handleGetSystemInfo} disabled={hardware.loading}>
            💻 获取系统信息
          </button>
          <button onClick={handleGetNetworkInfo} disabled={hardware.loading}>
            🌐 获取网络信息
          </button>
          <button onClick={handleVibrate} disabled={hardware.loading}>
            📳 震动测试
          </button>
          <button onClick={handleShowNotification} disabled={hardware.loading}>
            🔔 显示通知
          </button>
        </div>

        {/* 显示设备信息 */}
        {deviceInfo && (
          <div style={{ border: '1px solid #ddd', padding: '15px', margin: '10px 0' }}>
            <h3>📱 设备信息</h3>
            <p>
              <strong>设备名称:</strong> {deviceInfo.device_name}
            </p>
            <p>
              <strong>系统版本:</strong> {deviceInfo.os_version}
            </p>
            <p>
              <strong>应用版本:</strong> {deviceInfo.app_version}
            </p>
            <p>
              <strong>设备ID:</strong> {deviceInfo.device_id}
            </p>
            <p>
              <strong>电池电量:</strong> {deviceInfo.battery_level}%
            </p>
            <p>
              <strong>充电状态:</strong> {deviceInfo.is_charging ? '充电中' : '未充电'}
            </p>
          </div>
        )}

        {/* 显示位置信息 */}
        {locationInfo && (
          <div style={{ border: '1px solid #ddd', padding: '15px', margin: '10px 0' }}>
            <h3>📍 位置信息</h3>
            <p>
              <strong>纬度:</strong> {locationInfo.latitude}
            </p>
            <p>
              <strong>经度:</strong> {locationInfo.longitude}
            </p>
            <p>
              <strong>精度:</strong> {locationInfo.accuracy}m
            </p>
            <p>
              <strong>时间戳:</strong> {new Date(locationInfo.timestamp * 1000).toLocaleString()}
            </p>
          </div>
        )}

        {/* 显示系统信息 */}
        {systemInfo && (
          <div style={{ border: '1px solid #ddd', padding: '15px', margin: '10px 0' }}>
            <h3>💻 系统信息</h3>
            <p>
              <strong>总内存:</strong> {(systemInfo.total_memory / 1024 / 1024 / 1024).toFixed(1)}{' '}
              GB
            </p>
            <p>
              <strong>可用内存:</strong>{' '}
              {(systemInfo.available_memory / 1024 / 1024 / 1024).toFixed(1)} GB
            </p>
            <p>
              <strong>CPU使用率:</strong> {systemInfo.cpu_usage}%
            </p>
            <p>
              <strong>总存储:</strong> {(systemInfo.storage_total / 1024 / 1024 / 1024).toFixed(1)}{' '}
              GB
            </p>
            <p>
              <strong>可用存储:</strong>{' '}
              {(systemInfo.storage_available / 1024 / 1024 / 1024).toFixed(1)} GB
            </p>
          </div>
        )}

        {/* 显示网络信息 */}
        {networkInfo && (
          <div style={{ border: '1px solid #ddd', padding: '15px', margin: '10px 0' }}>
            <h3>🌐 网络信息</h3>
            <p>
              <strong>连接类型:</strong> {networkInfo.connection_type}
            </p>
            <p>
              <strong>连接状态:</strong> {networkInfo.is_connected ? '已连接' : '未连接'}
            </p>
            <p>
              <strong>信号强度:</strong> {networkInfo.signal_strength} dBm
            </p>
            <p>
              <strong>IP地址:</strong> {networkInfo.ip_address}
            </p>
          </div>
        )}
      </div>

      <p className="read-the-docs">
        上面是 API 调用演示，下面是硬件功能演示（需要在 Android 模拟器中测试）
      </p>
    </>
  );
}

export default App;

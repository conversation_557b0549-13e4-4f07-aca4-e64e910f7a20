import { useState } from 'react';
import reactLogo from './assets/react.svg';
import viteLogo from '/vite.svg';
import './App.css';
import { useApi, useApiWithParams } from './hooks/useApi';
import { userApi, User } from './api/userApi';

function App() {
  const [count, setCount] = useState(0);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  // 获取所有用户
  const { data: users, loading: usersLoading, error: usersError } = useApi(userApi.getAllUsers);

  // 获取单个用户详情
  const {
    data: userDetail,
    loading: userDetailLoading,
    execute: fetchUserDetail,
  } = useApiWithParams(userApi.getUserById);

  const handleUserClick = (userId: number) => {
    setSelectedUserId(userId);
    fetchUserDetail(userId);
  };

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Tauri + React + API Demo</h1>

      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>count is {count}</button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>

      {/* API 演示区域 */}
      <div className="api-demo">
        <h2>📡 API 调用演示</h2>

        {/* 用户列表 */}
        <div className="users-section">
          <h3>👥 用户列表</h3>
          {usersLoading && <p>加载用户中...</p>}
          {usersError && <p style={{ color: 'red' }}>错误: {usersError}</p>}
          {users && (
            <div className="users-grid">
              {users.slice(0, 6).map((user: User) => (
                <div
                  key={user.id}
                  className="user-card"
                  onClick={() => handleUserClick(user.id)}
                  style={{
                    border: '1px solid #ccc',
                    padding: '10px',
                    margin: '5px',
                    cursor: 'pointer',
                    backgroundColor: selectedUserId === user.id ? '#e3f2fd' : 'white',
                  }}
                >
                  <h4>{user.name}</h4>
                  <p>@{user.username}</p>
                  <p>{user.email}</p>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 用户详情 */}
        {selectedUserId && (
          <div className="user-detail">
            <h3>👤 用户详情</h3>
            {userDetailLoading && <p>加载详情中...</p>}
            {userDetail && (
              <div style={{ border: '1px solid #ddd', padding: '15px', marginTop: '10px' }}>
                <h4>{userDetail.name}</h4>
                <p>
                  <strong>用户名:</strong> {userDetail.username}
                </p>
                <p>
                  <strong>邮箱:</strong> {userDetail.email}
                </p>
                <p>
                  <strong>电话:</strong> {userDetail.phone}
                </p>
                <p>
                  <strong>网站:</strong> {userDetail.website}
                </p>
                <p>
                  <strong>公司:</strong> {userDetail.company.name}
                </p>
                <p>
                  <strong>地址:</strong> {userDetail.address.city}, {userDetail.address.street}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      <p className="read-the-docs">点击用户卡片查看详情 | 这是前端直接调用 API 的演示</p>
    </>
  );
}

export default App;

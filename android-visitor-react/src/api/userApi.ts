// 用户相关 API
import httpClient from './httpClient';

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  phone: string;
  website: string;
  company: {
    name: string;
    catchPhrase: string;
    bs: string;
  };
  address: {
    street: string;
    suite: string;
    city: string;
    zipcode: string;
    geo: {
      lat: string;
      lng: string;
    };
  };
}

export interface Post {
  userId: number;
  id: number;
  title: string;
  body: string;
}

// 用户 API 方法
export const userApi = {
  // 获取所有用户
  getAllUsers: async (): Promise<User[]> => {
    const response = await httpClient.get<User[]>('/users');
    return response.data;
  },

  // 获取单个用户
  getUserById: async (id: number): Promise<User> => {
    const response = await httpClient.get<User>(`/users/${id}`);
    return response.data;
  },

  // 获取用户的文章
  getUserPosts: async (userId: number): Promise<Post[]> => {
    const response = await httpClient.get<Post[]>(`/users/${userId}/posts`);
    return response.data;
  },

  // 创建用户
  createUser: async (userData: Omit<User, 'id'>): Promise<User> => {
    const response = await httpClient.post<User>('/users', userData);
    return response.data;
  },

  // 更新用户
  updateUser: async (id: number, userData: Partial<User>): Promise<User> => {
    const response = await httpClient.put<User>(`/users/${id}`, userData);
    return response.data;
  },

  // 删除用户
  deleteUser: async (id: number): Promise<void> => {
    await httpClient.delete(`/users/${id}`);
  },
};

export default userApi;
